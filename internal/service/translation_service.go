package service

import (
	"fmt"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/database"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/model"
	"github.com/sirupsen/logrus"
)

// TranslationService 翻译服务
type TranslationService struct {
	deepLService      *DeepLService
	aiService         *AITranslationService
	asyncQuotaService *AsyncQuotaService
}

// NewTranslationService 创建翻译服务
func NewTranslationService() *TranslationService {
	return &TranslationService{
		deepLService:      NewDeepLService(),
		aiService:         NewAITranslationService(),
		asyncQuotaService: NewAsyncQuotaService(),
	}
}

// Translate 翻译文本
func (s *TranslationService) Translate(userID string, req *dto.TranslationRequest) (*dto.TranslationResponse, error) {
	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 检查用户额度（不扣减，只检查）
	estimatedUsage := req.GetTotalCharCount()
	if err := s.checkUserQuota(userID, estimatedUsage); err != nil {
		return nil, err
	}

	serviceType := req.GetActualService()
	var translatedTexts map[string]string
	var usage *dto.TranslationUsage
	var err error

	// 根据服务类型选择翻译方式
	if serviceType.IsDeepLService() {
		// DeepL服务：使用批量翻译（DeepL支持一次请求多个文本）
		translatedTexts, usage, err = s.deepLService.TranslateBatch(req.Text, req.TargetLang)
	} else if serviceType.IsAIService() {
		// AI服务：合并为一次请求
		translatedTexts, usage, err = s.aiService.TranslateWithAI(req.Text, req.TargetLang, serviceType)
	} else {
		return nil, fmt.Errorf("unsupported service type: %s", req.Service)
	}

	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user_id": userID,
			"service": req.Service,
			"error":   err.Error(),
		}).Error("Translation failed")
		return nil, fmt.Errorf("translation failed: %w", err)
	}

	// 翻译成功后扣减实际用量
	actualUsage := usage.GetUsageCount()
	if err := s.asyncQuotaService.CheckAndDeductQuota(userID, actualUsage); err != nil {
		logrus.WithError(err).Error("Failed to deduct quota after successful translation")
		// 这里可以考虑记录到失败队列，稍后重试
		// 但不影响翻译结果的返回
	}

	// 记录用量（异步）
	go func() {
		if err := s.recordUsage(userID, req, usage); err != nil {
			logrus.WithError(err).Warn("Failed to record translation usage")
		}
	}()

	return &dto.TranslationResponse{
		Text: translatedTexts,
	}, nil
}

// checkUserQuota 检查用户额度（不扣减）
func (s *TranslationService) checkUserQuota(userID string, estimatedUsage int) error {
	return s.asyncQuotaService.CheckQuota(userID, estimatedUsage)
}



// recordUsage 记录翻译用量
func (s *TranslationService) recordUsage(userID string, req *dto.TranslationRequest, usage *dto.TranslationUsage) error {
	usageLog := &model.TranslationUsageLog{
		UserID:     userID,
		Service:    req.Service,
		CharCount:  usage.CharCount,
		UsageCount: usage.GetUsageCount(), // 等于字符数
		TargetLang: req.TargetLang,
		TextCount:  len(req.Text),
		CreatedAt:  time.Now(),
	}

	if err := database.DB.Create(usageLog).Error; err != nil {
		return fmt.Errorf("failed to record usage: %w", err)
	}

	return nil
}

// GetUserUsageInfo 获取用户用量信息（从异步服务获取）
func (s *TranslationService) GetUserUsageInfo(userID string) (*dto.UsageInfoResponse, error) {
	return s.asyncQuotaService.GetUserQuota(userID)
}
