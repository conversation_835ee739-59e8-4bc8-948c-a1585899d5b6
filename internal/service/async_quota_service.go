package service

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"sync"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/database"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/model"
	"github.com/sirupsen/logrus"
)

// 全局单例实例
var (
	GlobalAsyncQuotaService *AsyncQuotaService
	quotaServiceOnce        sync.Once
)

// QuotaDeductionItem 用量扣减项
type QuotaDeductionItem struct {
	UserID     string
	UsageCount int
	Timestamp  time.Time
}

// AsyncQuotaService 异步用量服务
type AsyncQuotaService struct {
	// 内存中的用量信息
	memoryQuotas map[string]*dto.UsageInfoResponse
	quotaMutex   sync.RWMutex

	// 异步扣减队列
	deductionQueue chan *QuotaDeductionItem
	batchSize      int
	flushInterval  time.Duration

	// 缓存服务
	cacheService *CacheService
}

func newAsyncQuotaService() *AsyncQuotaService {
	service := &AsyncQuotaService{
		memoryQuotas:   make(map[string]*dto.UsageInfoResponse),
		deductionQueue: make(chan *QuotaDeductionItem, 10000), // 队列容量10000
		batchSize:      100,                                   // 批量处理100条
		flushInterval:  5 * time.Second,                       // 5秒刷新一次
		cacheService:   GetGlobalCacheService(),
	}

	// 启动异步处理goroutine
	go service.startAsyncProcessor()

	return service
}

// GetGlobalAsyncQuotaService 获取全局AsyncQuotaService实例（单例）
func GetGlobalAsyncQuotaService() *AsyncQuotaService {
	quotaServiceOnce.Do(func() {
		GlobalAsyncQuotaService = newAsyncQuotaService()
	})
	return GlobalAsyncQuotaService
}

// resetQuotaInTransaction 在事务中重置用户额度
func (s *AsyncQuotaService) resetQuotaInTransaction(userID string) error {
	var quota model.UserAIQuota
	if err := database.DB.Where("user_id = ?", userID).First(&quota).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logrus.WithError(err).Error("Failed to get user quota from database")
			return fmt.Errorf("internal server error")
		}
		return nil
	}

	// 如果用户没有续订（总额度为0且无待生效额度），不进行重置
	if quota.TotalQuota == 0 && quota.PendingQuota == 0 {
		logrus.WithFields(logrus.Fields{
			"userId":       userID,
			"totalQuota":   quota.TotalQuota,
			"pendingQuota": quota.PendingQuota,
		}).Info("User has no subscription, skipping quota reset")
		return nil
	}

	err := database.DB.Transaction(func(tx *gorm.DB) error {
		// 记录重置前的状态
		oldUsedQuota := quota.UsedQuota
		oldTotalQuota := quota.TotalQuota
		oldPendingQuota := quota.PendingQuota
		oldNextResetAt := quota.NextResetAt

		// 执行重置逻辑
		quota.ResetUsage()
		now := time.Now()

		// 使用原子更新，只有当next_reset_at等于期望值时才更新
		result := tx.Model(&model.UserAIQuota{}).
			Where("user_id = ? AND next_reset_at = ?", userID, oldNextResetAt).
			Updates(map[string]interface{}{
				"total_quota":           quota.TotalQuota,
				"used_quota":            quota.UsedQuota,
				"pending_quota":         quota.PendingQuota,
				"cycle_start_at":        quota.CycleStartAt,
				"next_reset_at":         quota.NextResetAt,
				"pending_next_reset_at": quota.PendingNextResetAt,
				"original_reset_day":    quota.OriginalResetDay,
				"updated_at":            now,
			})

		if result.Error != nil {
			return fmt.Errorf("failed to reset user quota: %w", result.Error)
		}

		if result.RowsAffected == 0 {
			// 没有更新任何行，说明已经被其他请求重置了
			logrus.WithField("userId", userID).Info("Quota already reset by another request")
			// 重新加载最新数据
			if err := tx.Where("user_id = ?", userID).First(quota).Error; err != nil {
				return fmt.Errorf("failed to reload quota after concurrent reset: %w", err)
			}
			return nil
		}

		// 记录重置日志
		var reason string
		if oldPendingQuota > 0 {
			reason = fmt.Sprintf("monthly_reset_cleared_%d_used_applied_pending_%d_to_%d",
				oldUsedQuota, oldPendingQuota, quota.TotalQuota)
		} else {
			reason = fmt.Sprintf("monthly_reset_cleared_%d_used", oldUsedQuota)
		}
		resetLog := model.UserAIQuotaLog{
			UserId:    userID,
			QuotaCnt:  quota.TotalQuota,
			Reason:    reason,
			CreatedAt: now,
		}
		if err := tx.Create(&resetLog).Error; err != nil {
			return fmt.Errorf("failed to create reset log: %w", err)
		}

		logrus.WithFields(logrus.Fields{
			"userId":         userID,
			"oldTotalQuota":  oldTotalQuota,
			"newTotalQuota":  quota.TotalQuota,
			"clearedUsage":   oldUsedQuota,
			"appliedPending": oldPendingQuota,
		}).Info("User quota reset successfully")

		return nil
	})

	// 重置成功后清除内存和缓存
	if err == nil {
		_ = s.cacheService.InvalidateUsageInfoCache(userID)
		s.quotaMutex.Lock()
		delete(s.memoryQuotas, userID)
		s.quotaMutex.Unlock()
	}

	return err
}

// SetUserQuota 设置用户额度（用于首次订阅或续期）
func (s *AsyncQuotaService) SetUserQuota(tx *gorm.DB, userID string, totalQuota int, isRenewal bool, subscriptionExpiresAt int64) error {
	now := time.Now()

	var quota model.UserAIQuota
	err := tx.Where("user_id = ?", userID).First(&quota).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 首次订阅，创建新记录
		quota = model.UserAIQuota{
			UserId:       userID,
			TotalQuota:   totalQuota,
			UsedQuota:    0,
			PendingQuota: 0,
			CycleStartAt: now,
			CreatedAt:    now,
			UpdatedAt:    now,
		}
		// 优先使用订阅过期时间
		if subscriptionExpiresAt > 0 {
			subscriptionExpires := time.UnixMilli(subscriptionExpiresAt)
			if subscriptionExpires.After(now) {
				// 订阅过期时间有效，直接使用
				quota.NextResetAt = subscriptionExpires
				quota.OriginalResetDay = subscriptionExpires.Day()
			} else {
				// 订阅过期时间无效，使用Apple规则兜底
				quota.NextResetAt = quota.CalculateNextResetTime(now)
			}
		} else {
			// 没有提供订阅过期时间，使用Apple规则兜底
			quota.NextResetAt = quota.CalculateNextResetTime(now)
		}
		if err := tx.Create(&quota).Error; err != nil {
			return fmt.Errorf("failed to create user quota: %w", err)
		}
		// 记录日志
		log := model.UserAIQuotaLog{
			UserId:    userID,
			QuotaCnt:  totalQuota,
			Reason:    "initial_subscription",
			CreatedAt: now,
		}
		if err := tx.Create(&log).Error; err != nil {
			return fmt.Errorf("failed to create quota log: %w", err)
		}
	} else if err != nil {
		return fmt.Errorf("failed to get user quota: %w", err)
	} else {
		// 续期处理
		if quota.ShouldReset() {
			// 当前周期已结束，可以立即应用新额度并设置新的重置时间
			var nextResetTime time.Time
			var resetTimeSource string

			if subscriptionExpiresAt > 0 {
				subscriptionExpires := time.UnixMilli(subscriptionExpiresAt)
				if subscriptionExpires.After(now) {
					// 订阅过期时间有效，直接使用
					nextResetTime = subscriptionExpires
					resetTimeSource = "subscription_expires"
				} else {
					// 订阅过期时间无效，使用Apple规则兜底
					nextResetTime = quota.CalculateNextResetTime(now)
					resetTimeSource = "apple_rule_fallback"
				}
			} else {
				// 没有提供订阅过期时间，使用Apple规则兜底
				nextResetTime = quota.CalculateNextResetTime(now)
				resetTimeSource = "apple_rule_fallback"
			}

			quota.TotalQuota = totalQuota
			quota.PendingQuota = 0
			quota.NextResetAt = nextResetTime
			quota.OriginalResetDay = nextResetTime.Day()

			// 重置用量并更新周期时间
			quota.UsedQuota = 0
			quota.CycleStartAt = now
			quota.UpdatedAt = now

			// 记录日志
			reason := "renewal_immediate"
			if isRenewal {
				reason = "subscription_renewal_immediate"
			}
			log := model.UserAIQuotaLog{
				UserId:    userID,
				QuotaCnt:  totalQuota,
				Reason:    fmt.Sprintf("%s_cycle_ended_%s_%s", reason, resetTimeSource, nextResetTime.Format("2006-01-02")),
				CreatedAt: now,
			}
			if err := tx.Create(&log).Error; err != nil {
				return fmt.Errorf("failed to create quota log: %w", err)
			}
		} else {
			// 当前周期还存在，只添加到待生效额度，保持当前重置时间不变
			oldPending := quota.PendingQuota
			if oldPending == 0 {
				// 首次续期，直接设置待生效额度
				quota.PendingQuota = totalQuota
			} else {
				// 已有待生效额度，累加新额度
				quota.PendingQuota += totalQuota - oldPending
			}

			// 设置待生效的重置时间（在下次重置时生效）
			if subscriptionExpiresAt > 0 {
				subscriptionExpires := time.UnixMilli(subscriptionExpiresAt)
				if subscriptionExpires.After(now) {
					// 订阅过期时间有效，设置为待生效的重置时间
					quota.PendingNextResetAt = &subscriptionExpires
				}
				// 如果订阅过期时间无效，不设置待生效重置时间，重置时会使用Apple规则兜底
			}

			// 不更新NextResetAt，保持当前周期的重置时间
			quota.UpdatedAt = now

			// 记录日志
			reason := "renewal_pending"
			if isRenewal {
				reason = "subscription_renewal_pending"
			}
			var logReason string
			if quota.PendingNextResetAt != nil {
				logReason = fmt.Sprintf("%s_will_take_effect_on_%s_then_reset_to_%s",
					reason,
					quota.NextResetAt.Format("2006-01-02"),
					quota.PendingNextResetAt.Format("2006-01-02"))
			} else {
				logReason = fmt.Sprintf("%s_will_take_effect_on_%s", reason, quota.NextResetAt.Format("2006-01-02"))
			}
			log := model.UserAIQuotaLog{
				UserId:    userID,
				QuotaCnt:  0, // 待生效所以额度是0，只有重置时才真正增加
				Reason:    logReason,
				CreatedAt: now,
			}
			if err := tx.Create(&log).Error; err != nil {
				return fmt.Errorf("failed to create quota log: %w", err)
			}
		}
		// 保存更新
		if err := tx.Save(&quota).Error; err != nil {
			return fmt.Errorf("failed to update user quota: %w", err)
		}
	}

	_ = s.cacheService.InvalidateUsageInfoCache(userID)

	// 清除缓存，强制重新加载
	s.quotaMutex.Lock()
	delete(s.memoryQuotas, userID)
	s.quotaMutex.Unlock()

	logrus.WithFields(logrus.Fields{
		"userId":     userID,
		"totalQuota": totalQuota,
		"isRenewal":  isRenewal,
	}).Info("User quota set successfully")

	return nil
}

// GetUserQuota 获取用户用量信息（优先从内存，然后缓存，最后数据库）
func (s *AsyncQuotaService) GetUserQuota(userID string) (*dto.UsageInfoResponse, error) {
	// 1. 先从内存获取
	s.quotaMutex.RLock()
	if quota, exists := s.memoryQuotas[userID]; exists {
		s.quotaMutex.RUnlock()
		return quota, nil
	}
	s.quotaMutex.RUnlock()

	// 2. 从缓存获取
	if cachedQuota, err := s.cacheService.GetUsageInfoFromCache(userID); err == nil {
		// 加载到内存
		s.quotaMutex.Lock()
		s.memoryQuotas[userID] = cachedQuota
		s.quotaMutex.Unlock()
		return cachedQuota, nil
	}

	// 3. 从数据库获取并检查是否需要重置
	var aiQuota model.UserAIQuota
	if err := database.DB.Where("user_id = ?", userID).First(&aiQuota).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logrus.WithError(err).Error("Failed to get user quota from database")
			return nil, fmt.Errorf("internal server error")
		}
		// 如果用户没有额度记录，返回默认值
		defaultQuota := &dto.UsageInfoResponse{
			Limit:         0,
			Usage:         0,
			Remaining:     0,
			NextResetTime: 0,
		}
		// 缓存默认值
		s.quotaMutex.Lock()
		s.memoryQuotas[userID] = defaultQuota
		s.quotaMutex.Unlock()
		_ = s.cacheService.SetUsageInfoToCache(userID, defaultQuota)
		return defaultQuota, nil
	}

	// 检查是否需要重置
	if aiQuota.ShouldReset() {
		if resetErr := s.resetQuotaInTransaction(userID); resetErr != nil {
			logrus.WithError(resetErr).WithField("userId", userID).Error("Failed to reset quota")
		}
	}

	remaining := aiQuota.TotalQuota - aiQuota.UsedQuota
	nextResetTime := aiQuota.NextResetAt.UnixMilli()
	quota := &dto.UsageInfoResponse{
		Limit:         aiQuota.TotalQuota,
		Usage:         aiQuota.UsedQuota,
		Remaining:     remaining,
		NextResetTime: nextResetTime,
	}

	// 加载到内存和缓存
	s.quotaMutex.Lock()
	s.memoryQuotas[userID] = quota
	s.quotaMutex.Unlock()
	_ = s.cacheService.SetUsageInfoToCache(userID, quota)

	return quota, nil
}

// CheckQuota 只检查用量，不扣减
func (s *AsyncQuotaService) CheckQuota(userID string, usageCount int) error {
	quota, err := s.GetUserQuota(userID)
	if err != nil {
		return err
	}
	// 允许小幅超出：只有在剩余额度为0或负数时才拒绝
	if quota.Remaining <= 0 {
		return fmt.Errorf("insufficient quota: remaining %d, required %d", quota.Remaining, usageCount)
	}
	// 如果剩余额度大于0但不够扣减，允许本次超出使用
	return nil
}

// CheckAndDeductQuota 检查并扣减用量（先扣减内存，异步更新数据库）
func (s *AsyncQuotaService) CheckAndDeductQuota(userID string, usageCount int) error {
	s.quotaMutex.Lock()
	defer s.quotaMutex.Unlock()

	// 获取当前用量信息
	quota, exists := s.memoryQuotas[userID]
	if !exists {
		// 如果内存中没有，需要先释放锁去加载数据
		s.quotaMutex.Unlock()
		loadedQuota, err := s.GetUserQuota(userID)
		if err != nil {
			return err
		}
		s.quotaMutex.Lock()
		// 重新检查，可能在释放锁期间其他goroutine已经加载了
		if existingQuota, nowExists := s.memoryQuotas[userID]; nowExists {
			quota = existingQuota
		} else {
			quota = loadedQuota
			s.memoryQuotas[userID] = quota
		}
	}

	// 检查剩余额度（允许小幅超出）
	if quota.Remaining <= 0 {
		return fmt.Errorf("insufficient quota: remaining %d, required %d", quota.Remaining, usageCount)
	}
	// 如果剩余额度大于0但不够扣减，允许本次超出使用

	// 立即扣减内存中的值
	quota.Usage += usageCount
	quota.Remaining -= usageCount

	// 更新缓存
	_ = s.cacheService.SetUsageInfoToCache(userID, quota)

	// 异步扣减数据库
	select {
	case s.deductionQueue <- &QuotaDeductionItem{
		UserID:     userID,
		UsageCount: usageCount,
		Timestamp:  time.Now(),
	}:
		// 成功加入队列
	default:
		// 队列满了，记录警告但不阻塞
		logrus.WithFields(logrus.Fields{
			"user_id":     userID,
			"usage_count": usageCount,
		}).Warn("Deduction queue is full, item dropped")
	}

	return nil
}

// startAsyncProcessor 启动异步处理器
func (s *AsyncQuotaService) startAsyncProcessor() {
	ticker := time.NewTicker(s.flushInterval)
	defer ticker.Stop()
	batch := make([]*QuotaDeductionItem, 0, s.batchSize)
	for {
		select {
		case item := <-s.deductionQueue:
			batch = append(batch, item)
			// 如果批次满了，立即处理
			if len(batch) >= s.batchSize {
				s.processBatch(batch)
				batch = batch[:0] // 清空批次
			}
		case <-ticker.C:
			// 定时处理剩余的批次
			if len(batch) > 0 {
				s.processBatch(batch)
				batch = batch[:0] // 清空批次
			}
		}
	}
}

// processBatch 批量处理扣减
func (s *AsyncQuotaService) processBatch(batch []*QuotaDeductionItem) {
	if len(batch) == 0 {
		return
	}

	// 按用户ID聚合扣减量
	userDeductions := make(map[string]int)
	for _, item := range batch {
		userDeductions[item.UserID] += item.UsageCount
	}

	// 批量更新数据库
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			logrus.WithField("panic", r).Error("Panic in processBatch, rolling back transaction")
		}
	}()

	now := time.Now()
	for userID, totalDeduction := range userDeductions {
		// 更新用户AI额度（扣减用量）
		result := tx.Model(&model.UserAIQuota{}).
			Where("user_id = ?", userID).
			Updates(map[string]interface{}{
				"used_quota":   database.DB.Raw("used_quota + ?", totalDeduction),
				"last_used_at": now,
				"updated_at":   now,
			})

		if result.Error != nil {
			logrus.WithFields(logrus.Fields{
				"user_id":   userID,
				"deduction": totalDeduction,
				"error":     result.Error,
			}).Error("Failed to update user quota in database")
			tx.Rollback()
			return
		}

		if result.RowsAffected == 0 {
			logrus.WithFields(logrus.Fields{
				"user_id":   userID,
				"deduction": totalDeduction,
			}).Warn("No rows affected when updating user quota")
		}

		// 记录用量扣减日志
		quotaLog := model.UserAIQuotaLog{
			UserId:    userID,
			QuotaCnt:  -totalDeduction, // 负数表示扣减
			Reason:    fmt.Sprintf("translation_usage_batch_%d_chars", totalDeduction),
			CreatedAt: now,
		}

		if err := tx.Create(&quotaLog).Error; err != nil {
			logrus.WithFields(logrus.Fields{
				"user_id":   userID,
				"deduction": totalDeduction,
				"error":     err,
			}).Error("Failed to create quota log in database")
			tx.Rollback()
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logrus.WithError(err).Error("Failed to commit batch deduction transaction")
	} else {
		logrus.WithField("batch_size", len(batch)).Debug("Successfully processed quota deduction batch")
	}
}
